/**
 * handleSession函数 - 基于可用ReadableStream_Chain版本的纯TransformStream链式版本
 * 完全参考ReadableStream_Chain的可用逻辑，改成纯TransformStream实现
 */
export async function handleSession(request, env, ctx, protocolMode) {
    const { 0: client, 1: server } = Object.values(new WebSocketPair());
    server.accept();

    const earlyHeader = request.headers.get("sec-websocket-protocol") || "";
    let headerDone = false;
    let tcpWriter = null;
    let tcpReader = null;

    // 日志系统
    let address = '';
    let portWithRandomLog = '';
    const log = (info, event = '') => {
        console.log(`[${address}:${portWithRandomLog}] ${info}`, event || '');
    };

    // 纯TransformStream链式：完全模拟ReadableStream_Chain的逻辑
    new TransformStream({
        start(controller) {
            log('TransformStream链式处理启动');

            // 处理早期数据 - 完全按照ReadableStream_Chain的方式
            if (earlyHeader) {
                try {
                    const earlyData = decodeBase64Url(earlyHeader);
                    if (earlyData && earlyData.byteLength > 0) {
                        controller.enqueue(earlyData);
                        log(`处理早期数据: ${earlyData.byteLength} bytes`);
                    }
                } catch (e) {
                    log('早期数据解析失败', e.message);
                }
            }

            // WebSocket消息 → TransformStream - 完全按照ReadableStream_Chain的方式
            server.addEventListener("message", ({ data }) => {
                controller.enqueue(data);
            });

            // 关闭和错误处理 - 完全按照ReadableStream_Chain的方式
            server.addEventListener("close", () => {
                log('WebSocket连接关闭');
                controller.close();
            });

            server.addEventListener("error", (e) => {
                log('WebSocket错误', e);
                controller.error(e);
            });
        },

        async transform(chunk, controller) {
            try {
                /* ---------- 首包：解析协议 + 建TCP ---------- */
                if (!headerDone) {
                    headerDone = true;
                    log('开始处理协议头');

                    // 1) 解析协议头 - 保持Fixed版本的逻辑
                    const header = await parseProtocolHeader(chunk, server, protocolMode);
                    
                    address = header.addressRemote;
                    portWithRandomLog = `${header.portRemote}--${Math.random()}`;
                    log(`协议头解析成功: ${address}:${header.portRemote}`);

                    // 2) 建立TCP连接（含重试）- 保持Fixed版本的逻辑
                    try {
                        tcpInterface = await dial(header, globalControllerConfig.connectMode, protocolMode);
                        await tcpInterface.opened;
                        log(`首次连接成功: ${globalControllerConfig.connectMode}`);
                    } catch (connectError) {
                        log(`首次连接失败: ${connectError.message}`);
                        try {
                            tcpInterface = await dial(header, globalControllerConfig.retryMode, protocolMode);
                            await tcpInterface.opened;
                            log(`重试连接成功: ${globalControllerConfig.retryMode}`);
                        } catch (retryError) {
                            log(`重试连接失败: ${retryError.message}`);
                            throw retryError;
                        }
                    }

                    writer = tcpInterface.writable.getWriter();

                    // 3) 发送初始数据 - 保持Fixed版本的逻辑
                    if (header.rawClientData && header.rawClientData.byteLength > 0) {
                        await writer.write(header.rawClientData);
                        log(`发送初始数据: ${header.rawClientData.byteLength} bytes`);
                    }

                    // 4) 启动TCP到WebSocket的数据传输 - 保持Fixed版本的逻辑
                    (async () => {
                        try {
                            log('开始TCP到WebSocket数据传输');
                            // 使用TransformStream处理TCP到WebSocket - 保持Fixed版本的pipeThrough方式
                            await tcpInterface.readable.pipeThrough(new TransformStream({
                                transform(chunk, controller) {
                                    try {
                                        server.send(chunk);
                                        log(`发送TCP数据: ${chunk.byteLength} bytes`);
                                        return;
                                    } catch (e) {
                                        if (e instanceof TypeError) controller.error();
                                        throw e;
                                    }
                                },
                            })).pipeTo(new WritableStream());
                        } catch (e) {
                            log(`TCP到WebSocket传输错误: ${e.message}`);
                            if (server.readyState === WebSocket.OPEN) {
                                server.close(1011, 'Internal server error');
                            }
                        }
                    })();

                    return; // 首包处理完成
                }

                /* ---------- 后续数据：直接写TCP ---------- */
                if (writer) {
                    await writer.write(chunk);
                    log(`写入TCP数据: ${chunk.byteLength} bytes`);
                } else {
                    log('TCP连接未就绪');
                    throw new Error("TCP not ready");
                }
                
            } catch (error) {
                log(`Transform处理错误: ${error.message}`);
                controller.error(error);
            }
        },

        flush() {
            log('TransformStream链式处理结束');
            try { 
                writer?.close(); 
            } catch {}
        }
    })
    .readable
    .pipeTo(new WritableStream({
        write() {
            // 数据已在transform中处理，这里只是完成链式结构
        },
        close() {
            log('链式处理完成');
        },
        abort(reason) {
            log('链式处理中止', reason);
        }
    }))
    .catch(e => {
        // 整条管道出错 → 关闭WebSocket
        log(`链式管道错误: ${e.message}`);
        try { 
            server.close(1013, e.message); 
        } catch {}
    });

    return new Response(null, { status: 101, webSocket: client });
}

// 需要原_worker.js中的以下函数：
// - parseProtocolHeader
// - dial
// - decodeBase64Url
// - globalControllerConfig
