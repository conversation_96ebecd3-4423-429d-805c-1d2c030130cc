import { connect } from 'cloudflare:sockets';

// 保持原有的全局配置不变
const globalControllerConfig = {
    connectMode: 'direct',
    retryMode: 'relayip',
    targetProtocolType0: '18c-_-1b0-_-1bc-_-1d4-_-190-_-198-_-1b0-_-184-_-1c8-_-194-_-e8-_-1d8-_-1b0-_-194-_-1cc-_-1cc',
    targetProtocolType1: '18c-_-1b0-_-1bc-_-1d4-_-190-_-198-_-1b0-_-184-_-1c8-_-194-_-e8-_-1d0-_-1c8-_-1bc-_-1a8-_-184-_-1b8',
    targetPathType0: 'vlws',
    targetPathType1: 'trws',
};

const globalSessionConfig = {
    connect: {
        connectMode: 'direct',
        retryMode: 'relayip',
    },
    user: {
        id: '49f37b98-c37f-4d46-be93-1fe0a742dd43',
        pass: 'a233255z',
        sha224: '419023a775279d21cdbda41971c0bb52e962f11b4f4bfba6015a268b',
    },
    relay: {
        ip: 'jp.almain126.changeip.biz',
        _port: null,
        get port() {
            if (this._port !== null) {
                return this._port;
            }
            return this.ip.includes(':') ? this.ip.split(':')[1] : (null || undefined);
        },
        set port(value) {
            this._port = value;
        },
        socks: 'web5.serv00.com:13668',
    },
    misc: {
        subName: 'sub.txt',
    },
    api: {
        addresses: '',
        addresses2: '',
        directTemplate: '',
        globalTemplate: '',
    }
};

/**
 * 增强版 handleSession 函数 - 吸取其他文件的优点
 * @param {Request} request - HTTP请求对象
 * @param {any} env - 环境变量
 * @param {any} ctx - 执行上下文
 * @param {string} protocolMode - 协议模式
 * @returns {Promise<Response>} WebSocket响应
 */
export async function handleSession(request, env, ctx, protocolMode) {
    // 创建WebSocket对，使用现代化的解构方式
    const webSocketPair = new WebSocketPair();
    const [client, webSocket] = Object.values(webSocketPair);
    
    // 接受WebSocket连接
    webSocket.accept();
    
    // 获取早期数据头
    const earlyDataHeader = request.headers.get('sec-websocket-protocol') || '';
    
    // 日志记录系统 - 借鉴worker1.js的日志功能
    let address = '';
    let portWithRandomLog = '';
    const log = (info, event = '') => {
        console.log(`[${address}:${portWithRandomLog}] ${info}`, event);
    };
    
    // 远程连接包装器 - 借鉴worker1.js的设计
    let remoteSocketWrapper = {
        value: null,
    };
    
    let isDns = false;
    
    try {
        // 创建可读的WebSocket流 - 使用现代化Stream API
        const readableWebSocketStream = makeReadableWebSocketStream(webSocket, earlyDataHeader, log);
        
        // WebSocket到远程服务器的数据流处理 - 借鉴worker1.js的pipeTo模式
        readableWebSocketStream.pipeTo(new WritableStream({
            async write(chunk, controller) {
                try {
                    // DNS查询处理
                    if (isDns) {
                        return await handleDNSQuery(chunk, webSocket, null, log);
                    }
                    
                    // 如果已有远程连接，直接转发数据
                    if (remoteSocketWrapper.value) {
                        const writer = remoteSocketWrapper.value.writable.getWriter();
                        await writer.write(chunk);
                        writer.releaseLock();
                        return;
                    }
                    
                    // 解析协议头 - 保持原有的双协议支持
                    const header = await parseProtocolHeader(chunk, webSocket, protocolMode);
                    
                    address = header.addressRemote;
                    portWithRandomLog = `${header.portRemote}--${Math.random()} ${header.isUDP ? 'udp ' : 'tcp '}`;
                    
                    // UDP处理 - 借鉴worker1.js的UDP DNS支持
                    if (header.isUDP) {
                        if (header.portRemote === 53) {
                            isDns = true;
                            return handleDNSQuery(header.rawClientData, webSocket, new Uint8Array([header.version || 0, 0]), log);
                        } else {
                            throw new Error('UDP proxy only enable for DNS which is port 53');
                        }
                    }
                    
                    // 建立TCP连接 - 使用改进的连接逻辑
                    await handleTCPOutBound(remoteSocketWrapper, header, webSocket, log);
                    
                } catch (error) {
                    log(`处理数据块时出错: ${error.message}`);
                    throw error;
                }
            },
            
            close() {
                log('readableWebSocketStream已关闭');
            },
            
            abort(reason) {
                log('readableWebSocketStream被中止', reason);
            }
        })).catch(error => {
            log(`流处理错误: ${error.message}`);
            safeCloseWebSocket(webSocket);
        });
        
    } catch (error) {
        log(`handleSession错误: ${error.message}`);
        safeCloseWebSocket(webSocket);
        throw error;
    }
    
    return new Response(null, { status: 101, webSocket: client });
}

/**
 * 创建可读的WebSocket流 - 借鉴worker1.js的实现
 */
function makeReadableWebSocketStream(webSocketServer, earlyDataHeader, log) {
    let readableStreamCancel = false;
    const stream = new ReadableStream({
        start(controller) {
            webSocketServer.addEventListener('message', (event) => {
                if (readableStreamCancel) {
                    return;
                }
                const message = event.data;
                controller.enqueue(message);
            });
            
            webSocketServer.addEventListener('close', () => {
                safeCloseWebSocket(webSocketServer);
                if (readableStreamCancel) {
                    return;
                }
                controller.close();
            });
            
            webSocketServer.addEventListener('error', (err) => {
                log('webSocketServer错误', err);
                controller.error(err);
            });
            
            // 处理早期数据
            const { earlyData, error } = base64ToArrayBuffer(earlyDataHeader);
            if (error) {
                controller.error(error);
            } else if (earlyData) {
                controller.enqueue(earlyData);
            }
        },
        
        pull(controller) {
            // 当需要更多数据时调用
        },
        
        cancel(reason) {
            log('ReadableStream被取消', reason);
            readableStreamCancel = true;
            safeCloseWebSocket(webSocketServer);
        }
    });
    
    return stream;
}

/**
 * 处理TCP出站连接 - 整合原有逻辑和新的重试机制
 */
async function handleTCPOutBound(remoteSocketWrapper, header, webSocket, log) {
    // 创建响应头
    const vlessResponseHeader = new Uint8Array([header.version || 0, 0]);
    
    // 连接重试函数 - 借鉴worker1.js的重试机制
    async function connectWithRetry() {
        let tcpSocket;
        
        try {
            // 首次连接尝试
            tcpSocket = await createConnection(header, globalControllerConfig.connectMode, 
                header.protocolMode || globalControllerConfig.targetProtocolType0);
            await tcpSocket.opened;
            log(`首次连接成功: ${header.addressRemote}:${header.portRemote}`);
        } catch (error) {
            log(`首次连接失败，尝试重试模式: ${error.message}`);
            try {
                // 重试连接
                tcpSocket = await createConnection(header, globalControllerConfig.retryMode, 
                    header.protocolMode || globalControllerConfig.targetProtocolType0);
                await tcpSocket.opened;
                log(`重试连接成功: ${header.addressRemote}:${header.portRemote}`);
            } catch (retryError) {
                log(`重试连接也失败: ${retryError.message}`);
                throw retryError;
            }
        }
        
        return tcpSocket;
    }
    
    // 建立连接
    const tcpSocket = await connectWithRetry();
    remoteSocketWrapper.value = tcpSocket;
    
    // 写入初始数据
    if (header.rawClientData && header.rawClientData.byteLength > 0) {
        const writer = tcpSocket.writable.getWriter();
        await writer.write(header.rawClientData);
        writer.releaseLock();
    }
    
    // 设置连接关闭处理
    tcpSocket.closed.catch(error => {
        log('tcpSocket关闭错误', error);
    }).finally(() => {
        safeCloseWebSocket(webSocket);
    });
    
    // 远程到WebSocket的数据传输 - 简化版本
    remoteSocketToWS(tcpSocket, webSocket, vlessResponseHeader, null, log);
}

/**
 * 远程Socket到WebSocket的数据传输 - 借鉴worker1.js的实现
 */
async function remoteSocketToWS(remoteSocket, webSocket, vlessResponseHeader, retry, log) {
    try {
        let vlessHeader = vlessResponseHeader;
        let hasIncomingData = false;

        const readable = remoteSocket.readable;
        const reader = readable.getReader();

        while (true) {
            const { done, value } = await reader.read();
            hasIncomingData = true;

            if (done) {
                break;
            }

            if (value.byteLength === 0) {
                continue;
            }

            if (vlessHeader) {
                webSocket.send(await new Blob([vlessHeader, value]).arrayBuffer());
                vlessHeader = null;
            } else {
                webSocket.send(value);
            }
        }

    } catch (error) {
        log(`remoteSocketToWS错误: ${error.message}`);

        // 如果没有接收到数据且有重试函数，则执行重试
        if (!hasIncomingData && retry) {
            log('没有接收到数据，执行重试');
            retry();
            return;
        }
    } finally {
        safeCloseWebSocket(webSocket);
    }
}

/**
 * 安全关闭WebSocket连接
 */
function safeCloseWebSocket(socket) {
    try {
        if (socket.readyState === WebSocket.OPEN || socket.readyState === WebSocket.CONNECTING) {
            socket.close();
        }
    } catch (error) {
        console.error('关闭WebSocket时出错:', error);
    }
}

/**
 * Base64转ArrayBuffer - 借鉴worker1.js的实现
 */
function base64ToArrayBuffer(base64Str) {
    if (!base64Str) {
        return { error: null };
    }
    try {
        // 处理URL安全的Base64编码
        base64Str = base64Str.replace(/-/g, '+').replace(/_/g, '/');
        const decode = atob(base64Str);
        const arrayBuffer = Uint8Array.from(decode, (c) => c.charCodeAt(0));
        return { earlyData: arrayBuffer.buffer, error: null };
    } catch (error) {
        return { error };
    }
}

/**
 * 处理DNS查询 - 借鉴worker1.js的DNS处理逻辑
 */
async function handleDNSQuery(udpChunk, webSocket, vlessResponseHeader, log) {
    try {
        const dnsServer = '*******'; // 使用Google DNS
        const dnsPort = 53;
        let vlessHeader = vlessResponseHeader;

        const tcpSocket = connect({
            hostname: dnsServer,
            port: dnsPort,
        });

        log(`DNS查询连接到 ${dnsServer}:${dnsPort}`);

        const writer = tcpSocket.writable.getWriter();
        await writer.write(udpChunk);
        writer.releaseLock();

        const reader = tcpSocket.readable.getReader();
        const { done, value } = await reader.read();
        reader.releaseLock();

        if (!done && value) {
            if (vlessHeader) {
                webSocket.send(await new Blob([vlessHeader, value]).arrayBuffer());
            } else {
                webSocket.send(value);
            }
        }

        tcpSocket.close();

    } catch (error) {
        log(`DNS查询错误: ${error.message}`);
        throw error;
    }
}

/**
 * 解析协议头 - 保持原有的双协议支持，但简化实现
 */
async function parseProtocolHeader(buffer, wsInterface, protocolMode) {
    const bytes = new Uint8Array(buffer);
    const view = new DataView(buffer);
    const decoder = new TextDecoder();

    const addressTypeMap = protocolMode === globalControllerConfig.targetProtocolType0
        ? { IPv4: 1, DOMAIN: 2, IPv6: 3 }
        : { IPv4: 1, DOMAIN: 3, IPv6: 4 };

    if (protocolMode === globalControllerConfig.targetProtocolType0) {
        // VLESS协议处理
        const extractedID = bytes.subarray(1, 17);
        if (!matchUuid(extractedID, globalSessionConfig.user.id)) {
            if (wsInterface && wsInterface.close instanceof Function) {
                wsInterface.close(1013, 'Invalid user');
            }
            throw new Error(`Invalid user: UUID does not match`);
        }

        const version = bytes[0];
        const optionsLength = bytes[17];
        const commandIndex = 18 + optionsLength;
        const command = bytes[commandIndex]; // 0x01 TCP, 0x02 UDP, 0x03 MUX
        const portIndex = 18 + optionsLength + 1;
        const port = view.getUint16(portIndex);
        let offset = portIndex + 2;
        const addressType = bytes[offset++];

        const { hostname, offset: newOffset } = parseAddress(bytes, view, decoder, offset, addressType, addressTypeMap);
        const rawClientData = bytes.subarray(newOffset);

        // 发送响应头
        wsInterface.send(Uint8Array.of(version, 0).buffer);

        return {
            version,
            addressType,
            addressRemote: hostname,
            portRemote: port,
            rawClientData,
            isUDP: command === 0x02,
            protocolMode
        };

    } else if (protocolMode === globalControllerConfig.targetProtocolType1) {
        // 自定义协议处理
        const crLfIndex = 56;
        const extractedPassword = decoder.decode(bytes.subarray(0, crLfIndex));
        if (extractedPassword !== globalSessionConfig.user.sha224) {
            if (wsInterface && wsInterface.close instanceof Function) {
                wsInterface.close(1013, 'Invalid password');
            }
            throw new Error(`Invalid password`);
        }

        let offset = crLfIndex + 2;
        const command = bytes[offset++];
        const addressType = bytes[offset++];

        const { hostname, offset: newOffset } = parseAddress(bytes, view, decoder, offset, addressType, addressTypeMap);
        const port = view.getUint16(newOffset);
        const rawClientData = bytes.subarray(newOffset + 4);

        return {
            version: 1,
            addressType,
            addressRemote: hostname,
            portRemote: port,
            rawClientData,
            isUDP: command === 0x02,
            protocolMode
        };

    } else {
        throw new Error(`Unsupported protocol mode: ${protocolMode}`);
    }
}

/**
 * UUID匹配验证 - 保持原有实现
 */
function matchUuid(extractedID, uuidString) {
    uuidString = uuidString.replaceAll('-', '');
    for (let index = 0; index < 16; index++) {
        const expected = parseInt(uuidString.substring(index * 2, index * 2 + 2), 16);
        if (extractedID[index] !== expected) {
            return false;
        }
    }
    return true;
}

/**
 * 解析地址 - 保持原有实现
 */
function parseAddress(bytes, view, decoder, offset, addressType, addressTypeMap) {
    let hostname = '';

    switch (addressType) {
        case addressTypeMap.IPv4: // IPv4
            hostname = `${bytes[offset]}.${bytes[offset + 1]}.${bytes[offset + 2]}.${bytes[offset + 3]}`;
            offset += 4;
            break;

        case addressTypeMap.DOMAIN: // 域名
            const domainLength = bytes[offset++];
            hostname = decoder.decode(bytes.subarray(offset, offset + domainLength));
            offset += domainLength;
            break;

        case addressTypeMap.IPv6: // IPv6
            const ipv6 = [];
            for (let i = 0; i < 8; i++) {
                ipv6.push(view.getUint16(offset + i * 2).toString(16));
            }
            hostname = ipv6.join(':');
            offset += 16;
            break;

        default:
            throw new Error(`Unsupported address type: ${addressType}`);
    }

    return { hostname, offset };
}

/**
 * 创建连接 - 保持原有的多模式支持，但简化实现
 */
async function createConnection(header, mode, protocolMode) {
    const { addressType, addressRemote, portRemote } = header;
    const useTargetProtocol = protocolMode === globalControllerConfig.targetProtocolType0;

    switch (mode) {
        case 'relayip': {
            const needDirect =
                [1].includes(addressType) ||
                (useTargetProtocol && [3].includes(addressType)) ||
                (!useTargetProtocol && [4].includes(addressType));
            return needDirect
                ? connect({ hostname: addressRemote, port: portRemote })
                : connect({
                    hostname: globalSessionConfig.relay.ip,
                    port: globalSessionConfig.relay.port || portRemote,
                });
        }
        case 'relaysocks': {
            return socks5Connect(addressType, addressRemote, portRemote, useTargetProtocol);
        }
        case 'direct': {
            return connect({ hostname: addressRemote, port: portRemote });
        }
        default:
            return connect({ hostname: addressRemote, port: portRemote });
    }
}

/**
 * SOCKS5连接 - 保持原有实现但添加更好的错误处理
 */
async function socks5Connect(addressType, addressRemote, portRemote) {
    const { username, password, hostname, port } = socks5AddressParser(globalSessionConfig.relay.socks);
    let socket, reader, writer;
    const encoder = new TextEncoder();

    try {
        // 连接到SOCKS服务器
        socket = await connect({ hostname, port });
        reader = socket.readable.getReader();
        writer = socket.writable.getWriter();

        if (!reader || !writer) {
            throw new Error('无法获取SOCKS5读写器');
        }

        // 发送SOCKS5问候
        const socksGreeting = new Uint8Array([5, 2, 0, 2]); // 支持无认证和用户名/密码认证
        await writer.write(socksGreeting);

        // 读取服务器响应
        let res = (await reader.read()).value;
        if (res[0] !== 0x05) {
            throw new Error('SOCKS5握手失败');
        }

        // 处理认证
        if (res[1] === 0x02 && username && password) {
            // 用户名/密码认证
            const authRequest = new Uint8Array([
                1,
                username.length,
                ...encoder.encode(username),
                password.length,
                ...encoder.encode(password)
            ]);
            await writer.write(authRequest);

            res = (await reader.read()).value;
            if (res[1] !== 0x00) {
                throw new Error('SOCKS5认证失败');
            }
        }

        // 构建目标地址
        let DSTADDR;
        switch (addressType) {
            case 1: // IPv4
                DSTADDR = new Uint8Array([1, ...addressRemote.split('.').map(Number)]);
                break;
            case 2: // 域名
                DSTADDR = new Uint8Array([3, addressRemote.length, ...encoder.encode(addressRemote)]);
                break;
            case 3: // IPv6
                DSTADDR = new Uint8Array([4, ...addressRemote.split(':').flatMap(x => [parseInt(x.slice(0, 2), 16), parseInt(x.slice(2), 16)])]);
                break;
            default:
                throw new Error(`不支持的地址类型: ${addressType}`);
        }

        // 发送连接请求
        const socksRequest = new Uint8Array([5, 1, 0, ...DSTADDR, portRemote >> 8, portRemote & 0xff]);
        await writer.write(socksRequest);

        // 读取最终响应
        res = (await reader.read()).value;
        if (res[1] !== 0x00) {
            throw new Error('SOCKS5连接请求失败');
        }

        reader.releaseLock();
        writer.releaseLock();
        return socket;

    } catch (error) {
        // 清理资源
        if (reader) reader.releaseLock();
        if (writer) writer.releaseLock();
        if (socket) {
            try {
                await socket.close();
            } catch (closeError) {
                console.error('关闭SOCKS5连接时出错:', closeError);
            }
        }
        throw error;
    }
}

/**
 * SOCKS5地址解析器 - 简化版本
 */
function socks5AddressParser(address) {
    let [hostPort, auth] = address.includes('@') ? address.split('@').reverse() : [address, ''];
    let [username, password] = auth ? auth.split(':') : ['', ''];
    let [hostname, port] = hostPort.split(':');

    return {
        username: username || '',
        password: password || '',
        hostname: hostname || '',
        port: parseInt(port) || 1080
    };
}

// ============================================================================
// 使用说明和改进点总结
// ============================================================================

/**
 * 增强版 handleSession 函数的主要改进：
 *
 * 1. 【从 worker1.js 吸取的优点】：
 *    - 现代化的 Stream API 使用 (makeReadableWebSocketStream + pipeTo)
 *    - 更好的重试机制和错误恢复
 *    - 详细的日志记录功能
 *    - 更清晰的代码结构和注释
 *    - 更好的资源清理机制
 *    - UDP DNS 查询支持
 *
 * 2. 【从 worker.js 吸取的优点】：
 *    - 更简洁的错误处理机制
 *    - 更直观的连接重试逻辑
 *    - 简化的数据传输管道
 *    - Base64 解码的优化处理
 *
 * 3. 【保持的 _worker.js 原有优势】：
 *    - 双协议支持能力 (VLESS + 自定义协议)
 *    - 多种连接模式支持 (direct, relayip, relaysocks)
 *    - 高度可配置性
 *    - 完善的验证机制
 *    - 函数签名完全不变，保持兼容性
 *
 * 4. 【核心改进】：
 *    - 简化了复杂的 ingressMode 和 upstreamMode 配置
 *    - 使用现代化的 Stream API 替代复杂的流处理逻辑
 *    - 改进了错误处理和重试机制
 *    - 添加了详细的日志记录
 *    - 优化了资源管理和清理
 *    - 保持了所有原有功能的同时提高了代码可读性
 *
 * 使用方法：
 * 1. 将此文件中的 handleSession 函数替换原 _worker.js 中的同名函数
 * 2. 确保所有依赖的全局配置和辅助函数都已包含
 * 3. 函数签名和调用方式完全不变
 * 4. 支持所有原有的协议模式和连接方式
 */

// 导出配置对象供外部使用
export { globalControllerConfig, globalSessionConfig };
