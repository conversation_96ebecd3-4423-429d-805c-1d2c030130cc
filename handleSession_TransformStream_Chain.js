/**
 * handleSession函数 - TransformStream真正链式版本
 * 基于_worker.js原函数，参考您提供的链式代码风格
 */
export async function handleSession(request, env, ctx, protocolMode) {
    const { 0: client, 1: server } = Object.values(new WebSocketPair());
    server.accept();

    const earlyHeader = request.headers.get("sec-websocket-protocol") || "";
    let headerDone = false;
    let tcpWriter = null;
    let tcpReader = null;

    // 日志系统
    let address = '';
    let portWithRandomLog = '';
    const log = (info, event = '') => {
        console.log(`[${address}:${portWithRandomLog}] ${info}`, event || '');
    };

    // 直接链式：ReadableStream → TransformStream → WritableStream
    new ReadableStream({
        start(controller) {
            log('WebSocket ReadableStream启动');
            
            // 处理早期数据
            if (earlyHeader) {
                try {
                    const earlyData = decodeBase64Url(earlyHeader);
                    if (earlyData && earlyData.byteLength > 0) {
                        controller.enqueue(earlyData);
                        log(`处理早期数据: ${earlyData.byteLength} bytes`);
                    }
                } catch (e) {
                    log('早期数据解析失败', e.message);
                }
            }

            // WebSocket消息 → ReadableStream
            server.addEventListener("message", ({ data }) => {
                controller.enqueue(data);
            });

            // 关闭和错误处理
            server.addEventListener("close", () => {
                log('WebSocket连接关闭');
                controller.close();
            });
            
            server.addEventListener("error", (e) => {
                log('WebSocket错误', e);
                controller.error(e);
            });
        },
    })
    .pipeThrough(
        // TransformStream进行数据处理
        new TransformStream({
            start() {
                log('TransformStream启动');
            },

            async transform(chunk, controller) {
                try {
                    /* ---------- 首包：解析协议 + 建TCP ---------- */
                    if (!headerDone) {
                        headerDone = true;
                        log('开始处理协议头');

                        // 1) 解析协议头 - 使用原_worker.js的函数
                        const header = await parseProtocolHeader(chunk, server, protocolMode);
                        
                        address = header.addressRemote;
                        portWithRandomLog = `${header.portRemote}--${Math.random()}`;
                        log(`协议头解析成功: ${address}:${header.portRemote}`);

                        // 2) 建立TCP连接（含重试）- 使用原_worker.js的逻辑
                        let tcpInterface;
                        try {
                            tcpInterface = await dial(header, globalControllerConfig.connectMode, protocolMode);
                            await tcpInterface.opened;
                            log(`首次连接成功: ${globalControllerConfig.connectMode}`);
                        } catch (connectError) {
                            log(`首次连接失败: ${connectError.message}`);
                            try {
                                tcpInterface = await dial(header, globalControllerConfig.retryMode, protocolMode);
                                await tcpInterface.opened;
                                log(`重试连接成功: ${globalControllerConfig.retryMode}`);
                            } catch (retryError) {
                                log(`重试连接失败: ${retryError.message}`);
                                throw retryError;
                            }
                        }

                        // 3) 准备reader/writer
                        tcpWriter = tcpInterface.writable.getWriter();
                        tcpReader = tcpInterface.readable.getReader();

                        // 4) 发送握手剩余数据
                        if (header.rawClientData && header.rawClientData.byteLength > 0) {
                            await tcpWriter.write(header.rawClientData);
                            log(`发送初始数据: ${header.rawClientData.byteLength} bytes`);
                        }

                        // 5) 单独异步任务：TCP → WebSocket
                        (async () => {
                            try {
                                log('开始TCP到WebSocket数据传输');
                                while (true) {
                                    const { value, done } = await tcpReader.read();
                                    if (done) {
                                        log('TCP数据读取完成');
                                        break;
                                    }
                                    if (value && value.byteLength > 0) {
                                        server.send(value);
                                        log(`发送TCP数据: ${value.byteLength} bytes`);
                                    }
                                }
                            } catch (e) {
                                log(`TCP到WebSocket传输错误: ${e.message}`);
                                try { 
                                    server.close(1013, e.message); 
                                } catch {}
                            } finally {
                                try { 
                                    tcpReader.releaseLock(); 
                                } catch {}
                            }
                        })();

                        // 首包处理完成，传递给下游
                        controller.enqueue(chunk);
                        return;
                    }

                    /* ---------- 后续数据：通过Transform传递 ---------- */
                    controller.enqueue(chunk);
                    
                } catch (error) {
                    log(`Transform处理错误: ${error.message}`);
                    controller.error(error);
                }
            },

            flush() {
                log('TransformStream结束');
            }
        })
    )
    .pipeTo(
        // WritableStream处理最终数据写入
        new WritableStream({
            async write(chunk) {
                /* ---------- 后续数据：直接写TCP ---------- */
                if (tcpWriter) {
                    await tcpWriter.write(chunk);
                    log(`写入TCP数据: ${chunk.byteLength} bytes`);
                } else {
                    log('TCP连接未就绪');
                    throw new Error("TCP not ready");
                }
            },

            close() {
                log('WritableStream关闭');
                try { 
                    tcpWriter?.close(); 
                } catch {}
            },

            abort(e) {
                log('WritableStream中止', e);
                try { 
                    tcpWriter?.abort(e); 
                } catch {}
            },
        })
    )
    .catch(e => {
        // 整条管道出错 → 关闭WebSocket
        log(`管道错误: ${e.message}`);
        try { 
            server.close(1013, e.message); 
        } catch {}
    });

    return new Response(null, { status: 101, webSocket: client });
}

// 需要原_worker.js中的以下函数：
// - parseProtocolHeader
// - dial
// - decodeBase64Url
// - globalControllerConfig
